package com.navigator.cuckoo.listener;

import com.azure.messaging.servicebus.ServiceBusProcessorClient;
import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusReceivedMessage;
import com.azure.messaging.servicebus.ServiceBusReceivedMessageContext;
import com.azure.messaging.servicebus.ServiceBusErrorContext;
import com.google.gson.Gson;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Azure Service Bus 原生消息监听器
 * 用于接收通过 ServiceBusSenderClient 发送的定时消息
 * 
 * <AUTHOR>
 * @since 2025-1-7
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceBusMessageListener {

    @Value("${spring.jms.servicebus.connection-string}")
    private String connectionString;

    @Value("${messageQueue.atlas.syncQueueName}")
    private String syncQueueName;

    private final AtlasSyncUriService uriService;
    private ServiceBusProcessorClient processorClient;

    @PostConstruct
    public void initialize() {
        try {
            // 创建 Service Bus 处理器客户端
            processorClient = new ServiceBusClientBuilder()
                    .connectionString(connectionString)
                    .processor()
                    .queueName(syncQueueName)
                    .processMessage(this::processMessage)
                    .processError(this::processError)
                    .buildProcessorClient();

            // 启动消息处理器
            processorClient.start();
            log.info("Azure Service Bus 消息监听器已启动，队列: {}", syncQueueName);
            
        } catch (Exception e) {
            log.error("初始化 Azure Service Bus 监听器失败: {}", e.getMessage(), e);
        }
    }

    @PreDestroy
    public void cleanup() {
        if (processorClient != null) {
            processorClient.close();
            log.info("Azure Service Bus 消息监听器已关闭");
        }
    }

    /**
     * 处理接收到的消息
     */
    private void processMessage(ServiceBusReceivedMessageContext context) {
        ServiceBusReceivedMessage message = context.getMessage();
        String messageBody = message.getBody().toString();
        
        log.info("===============[ServiceBus-{}] Message received : {}===============", 
                syncQueueName, messageBody);

        try {
            // 检查消息类型
            String messageType = (String) message.getApplicationProperties().get("messageType");
            
            if ("scheduledQuery".equals(messageType)) {
                handleScheduledQueryMessage(messageBody);
            } else {
                log.warn("未知的消息类型: {}", messageType);
            }

            // 完成消息处理
            context.complete();
            
        } catch (Exception e) {
            log.error("处理 Service Bus 消息时发生错误: {}", e.getMessage(), e);
            // 放弃消息处理，消息将进入死信队列
            context.abandon();
        }
    }

    /**
     * 处理错误
     */
    private void processError(ServiceBusErrorContext context) {
        log.error("Service Bus 消息处理错误: {}", context.getException().getMessage(), context.getException());
    }

    /**
     * 处理定时查询消息
     */
    private void handleScheduledQueryMessage(String message) {
        try {
            Gson gson = new Gson();
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = gson.fromJson(message, Map.class);
            
            String businessEntity = (String) messageData.get("businessEntity");
            String contractCode = (String) messageData.get("contractCode");
            String action = (String) messageData.get("action");

            if ("getContractOpenQuantity".equals(action)) {
                // 执行合同开放数量查询
                BigDecimal result = uriService.getContractOpenQuantity(businessEntity, contractCode);
                log.info("Service Bus 定时查询合同openQuantity结果: {}, 合同: {}, 账套: {}",
                        result, contractCode, businessEntity);
            } else {
                log.warn("未知的消息动作: {}", action);
            }

        } catch (Exception e) {
            log.error("处理 Service Bus 定时查询消息时发生错误: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让消息进入重试或死信队列
        }
    }
}
