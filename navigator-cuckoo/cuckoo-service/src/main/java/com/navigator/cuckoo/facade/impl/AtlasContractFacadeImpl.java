package com.navigator.cuckoo.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.Gson;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.vo.AtlasSyncRecordVO;
import com.navigator.cuckoo.service.*;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
// 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end

/**
 * <p>
 * 对外暴露接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */

@Slf4j
@RestController
@RequiredArgsConstructor
public class AtlasContractFacadeImpl implements AtlasContractFacade {
    private final IAtlasSyncService syncService;
    private final IAtlasSyncQueryService queryService;
    private final IAtlasSyncRetryService retryService;
    private final IAtlasMappingService configService;
    private final AtlasSyncUriService syncUriService;
    private final AtlasTradeRemoteService tradeRemoteService;

    // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 start
    private final ScheduledMessageService scheduledMessageService;

    @Value("${messageQueue.atlas.syncQueueName}")
    private String atlasQueueName;
    // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end

    @Override
    public Result syncContractRequest(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【trade】==》【cuckoo】\n method:syncContractRequest,params:{}", new Gson().toJson(syncRequestDTO));
        syncService.syncContractRequest(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result syncDeliveryRequest(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【trade】==》【cuckoo】\n method:syncDeliveryRequest,params:{}", new Gson().toJson(syncRequestDTO));
        syncService.syncContractRequest(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result<BigDecimal> getContractOpenQuantity(String businessEntity, String contractCode) {
        return Result.success(syncUriService.getContractOpenQuantity(businessEntity, contractCode));
    }

    @Override
    public Result<AtlasDeliveryOpenQuantityDTO> getDeliveryOpenQuantity(String counterpartyId, AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO) {
        return Result.success(syncUriService.getDeliveryOpenQuantity(counterpartyId, deliveryOpenQuantityDTO));
    }

    @Override
    public Result<AtlasMappingContractEntity> getByNavContractCode(String contractCode) {
        return Result.success(configService.getByNavContractCode(contractCode));
    }

    @Override
    public Result updateAtlasMappingContract(AtlasMappingContractEntity atlasMappingContractEntity) {
        return Result.success(configService.updateAtlasMappingContract(atlasMappingContractEntity));
    }

    @Override
    public Result<BigDecimal> getContractBlockedQuantity(String contractCode) {
        return Result.success(syncUriService.getContractBlockedQuantity(contractCode));
    }

    /**
     * 延时查询合同openQuantity - 实验功能
     */
    @GetMapping("/getContractOpenQuantityDelayed")
    public Result<String> getContractOpenQuantityDelayed(
            @RequestParam("businessEntity") String businessEntity,
            @RequestParam("contractCode") String contractCode,
            @RequestParam(value = "delaySeconds", defaultValue = "0") int delaySeconds) {

        if (delaySeconds > 0) {
            // 延时处理：使用线程延时后再调用
            new Thread(() -> {
                try {
                    Thread.sleep(delaySeconds * 1000L); // 秒转换为毫秒
                    BigDecimal result = syncUriService.getContractOpenQuantity(businessEntity, contractCode);
                    log.info("延时{}秒后查询合同openQuantity结果: {}, 合同: {}", delaySeconds, result, contractCode);
                } catch (InterruptedException e) {
                    log.error("延时查询被中断: {}", e.getMessage());
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("延时查询失败: {}", e.getMessage());
                }
            }).start();

            return Result.success("延时查询已启动，将在" + delaySeconds + "秒后执行");
        } else {
            // 立即查询
            BigDecimal result = syncUriService.getContractOpenQuantity(businessEntity, contractCode);
            return Result.success("立即查询结果: " + result);
        }
    }

    /**
     * 定时查询合同openQuantity - 支持指定具体投递时间
     * 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 start
     */
    @GetMapping("/getContractOpenQuantityFlexDelay")
    public Result<String> getContractOpenQuantityFlexDelay(
            @RequestParam("businessEntity") String businessEntity,
            @RequestParam("contractCode") String contractCode,
            @RequestParam(value = "scheduledTime", required = false) String scheduledTime) {

        try {
            if (scheduledTime != null && !scheduledTime.trim().isEmpty()) {
                // 使用优化后的 Azure Service Bus 定时投递消息
                Map<String, Object> messageData = scheduledMessageService.createContractQueryMessage(
                    businessEntity, contractCode, "getContractOpenQuantity");

                // 发送定时消息，返回消息ID用于可能的取消操作
                String messageId = scheduledMessageService.sendScheduledMessage(atlasQueueName, messageData, scheduledTime);

                return Result.success(String.format("定时查询已安排，将在%s执行，消息ID: %s", scheduledTime, messageId));
            } else {
                // 立即查询
                BigDecimal result = syncUriService.getContractOpenQuantity(businessEntity, contractCode);
                return Result.success("立即查询结果: " + result);
            }
        } catch (Exception e) {
            log.error("定时查询设置失败: {}", e.getMessage(), e);
            return Result.failure("定时查询设置失败: " + e.getMessage());
        }
    }

    /**
     * 取消定时查询消息 - 新增功能
     */
    @GetMapping("/cancelScheduledQuery")
    public Result<String> cancelScheduledQuery(@RequestParam("messageId") String messageId) {
        try {
            boolean cancelled = scheduledMessageService.cancelScheduledMessage(messageId);
            if (cancelled) {
                return Result.success("定时查询已成功取消，消息ID: " + messageId);
            } else {
                return Result.failure("取消定时查询失败，可能消息已执行或不存在，消息ID: " + messageId);
            }
        } catch (Exception e) {
            log.error("取消定时查询失败: {}", e.getMessage(), e);
            return Result.failure("取消定时查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询当前已调度消息数量 - 监控功能
     */
    @GetMapping("/getScheduledMessageCount")
    public Result<Integer> getScheduledMessageCount() {
        try {
            int count = scheduledMessageService.getScheduledMessageCount();
            return Result.success(count);
        } catch (Exception e) {
            log.error("查询已调度消息数量失败: {}", e.getMessage(), e);
            return Result.failure("查询失败: " + e.getMessage());
        }
    }
    // 000000 优化延时投递消息 changed by Jason Shi at 2025-1-7 end

    @Override
    public Result getSyncRecordList(QueryDTO<AtlasQueryDTO> queryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:getSyncRecordList,params:{}", new Gson().toJson(queryDTO));
        IPage<AtlasSyncRecordVO> recordVOIPage = queryService.getSyncRecordList(queryDTO);
        return Result.page(recordVOIPage);
    }

    @Override
    public Result reSyncContractRequest(AtlasRetryDTO atlasRetryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:reSyncContractRequest,params:{}", new Gson().toJson(atlasRetryDTO));
        retryService.reSyncContractRequest(atlasRetryDTO);
        return Result.success();
    }

    @Override
    public Result<List<String>> getBusinessEntityNameList() {
        log.info("【magellan】==》【cuckoo】\n method:getBusinessEntityNameList");
        return Result.success(tradeRemoteService.getBusinessEntityNameList());
    }

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    @Override
    public Result<List<String>> getOperationTypeList() {
        log.info("【magellan】==》【cuckoo】\n method:getOperationTypeList");
        return Result.success(queryService.getOperationTypeList());
    }
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End

    @Override
    public Result getMappingContractList(QueryDTO<AtlasMappingQueryDTO> queryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:getSyncRecordList,params:{}", new Gson().toJson(queryDTO));
        IPage<AtlasMappingContractEntity> recordVOIPage = queryService.getMappingContractList(queryDTO);
        return Result.page(recordVOIPage);
    }

    @Override
    public Result rebuildRequestEntity(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【PostMan】\n method:reSyncContractRequest,params:{}", new Gson().toJson(syncRequestDTO));
        retryService.rebuildRequestEntity(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result reSyncByRequestId(Integer requestId) {
        log.info("【PostMan】\n method:reSyncByRequestId,requestId:{}", requestId);
        retryService.reSyncByRequestId(requestId);
        return Result.success();
    }

    @Override
    public Result reSyncByRecordId(Integer recordId) {
        log.info("【PostMan】\n method:reSyncByRecordId,recordId:{}", recordId);
        retryService.reSyncByRecordId(recordId);
        return Result.success();
    }

    @Override
    public Result<String> importAtlasDepartmentMapping(MultipartFile file) {
        return Result.success(configService.importAtlasDepartmentMapping(file));
    }
}
