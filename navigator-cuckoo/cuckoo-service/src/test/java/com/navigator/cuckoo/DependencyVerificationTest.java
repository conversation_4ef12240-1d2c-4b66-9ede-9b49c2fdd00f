package com.navigator.cuckoo;

import org.junit.jupiter.api.Test;
import reactor.core.publisher.Sinks;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证依赖是否正确加载的测试
 */
public class DependencyVerificationTest {

    @Test
    public void testReactorCoreSinksClassExists() {
        // 验证 Sinks 类是否存在并可以实例化
        assertDoesNotThrow(() -> {
            Sinks.Many<String> sink = Sinks.many().multicast().onBackpressureBuffer();
            assertNotNull(sink);
        });
    }

    @Test
    public void testAzureServiceBusClassExists() {
        // 验证 Azure Service Bus 相关类是否存在
        assertDoesNotThrow(() -> {
            Class.forName("com.azure.messaging.servicebus.ServiceBusClientBuilder");
            Class.forName("com.azure.messaging.servicebus.ServiceBusSenderClient");
            Class.forName("com.azure.messaging.servicebus.ServiceBusMessage");
        });
    }
}
